// React must be in scope when using JSX
import React, { useState, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './App.css';
import Dashboard from './components/dashboard/Dashboard';
import PerformanceMetrics from './components/events/PerformanceMetrics';
import MainSidebar from './components/sidebar/MainSidebar';
import EventsSidebar from './components/events/EventsSidebar';
import ArchiveSidebar from './components/archive/ArchiveSidebar';
import ConfigurationSidebar from './components/configuration/ConfigurationSidebar';
import SettingsSidebar from './components/settings/SettingsSidebar';
import UniversalSidebar from './components/sidebar/UniversalSidebar';
import EventsContent from './components/events/EventsContent';
import ArchivePlaybackContent from './components/archive/ArchivePlaybackContent';
import SettingsContent from './components/settings/SettingsContent';
import { CameraProvider } from './components/camera/CameraManager';
import CamerasContent from './components/configuration/CamerasContent';
import MediaServerContent from './components/configuration/MediaServerContent';
import AnalyticalServerContent from './components/configuration/AnalyticalServerContent';
import VideoDecoderContent from './components/configuration/VideoDecoderContent';
import DRSitesContent from './components/configuration/DRSitesContent';
import AlertsContent from './components/configuration/AlertsContent';
import UserAccessManagement from './components/users/UserAccessManagement';
import IllustratedLogin from './components/auth/IllustratedLogin';
import { useCameraStore } from './store/cameraStore';
import { useUserStore } from './store/userStore';

function TabBar({ activeTab, onTabChange, onLogout, currentUser }) {
  // Keep Archive tab for on-demand recordings
  const tabs = ['Dashboard', 'Events', 'Archive', 'Configuration', 'Settings'];

  return (
    <div className="tab-bar">
      <div className="tabs-container">
        {tabs.map((tab) => (
          <button
            key={tab}
            className={`tab-button ${activeTab === tab ? 'active' : ''}`}
            onClick={() => onTabChange(tab)}
          >
            {tab}
          </button>
        ))}
      </div>
      <div className="right-controls">
        <div className="user-controls">
          {currentUser && (
            <div className="user-info">
              <span className="username">{currentUser.username}</span>
              <span className="role-badge">{currentUser.role}</span>
              <button className="logout-button" onClick={onLogout}>
                Logout
              </button>
            </div>
          )}
        </div>
        <PerformanceMetrics />
      </div>
    </div>
  );
}

function TabContent({ activeTab, currentView, onViewChange, showCollectionManager, setShowCollectionManager, settingsMenu, selectedRecordingId, eventsMenu, onSelectRecording }) {
  console.log('TabContent rendering with activeTab:', activeTab, 'currentView:', currentView);
  switch (activeTab) {
    case 'Configuration':
      return (
        <div className="configuration-content">
          {currentView === 'cameras' ? <CamerasContent selectedMenu={currentView} /> : null}
          {currentView === 'media-server' ? <MediaServerContent selectedMenu={currentView} /> : null}
          {currentView === 'analytics-server' ? <AnalyticalServerContent selectedMenu={currentView} /> : null}
          {currentView === 'video-decoder-connector' ? <VideoDecoderContent selectedMenu={currentView} /> : null}
          {currentView === 'user-access-levels' ? <UserAccessManagement /> : null}
          {currentView === 'replication-policy' ? <DRSitesContent selectedMenu={currentView} /> : null}
          {currentView === 'sender-configuration' ? <AlertsContent selectedMenu={currentView} /> : null}
        </div>
      );
    case 'Dashboard':
      return (
        <div className="dashboard-content">
          <Dashboard
            currentView={currentView}
            onViewChange={onViewChange}
            showCollectionManager={showCollectionManager}
            setShowCollectionManager={setShowCollectionManager}
            activeTab={activeTab}
          />
        </div>
      );
    case 'Events':
      return (
        <div className="events-content">
          <EventsContent selectedMenu={eventsMenu} />
        </div>
      );
    case 'Archive':
      return (
        <div className="archive-content">
          {currentView === 'current-recordings' && <CurrentRecordings />}
          {(currentView === 'archive-playback' || !currentView || currentView === 'camera') && (
            <ArchivePlaybackContent
              selectedRecordingId={selectedRecordingId}
              onSelectRecording={onSelectRecording}
            />
          )}
          {/* Other archive menu items can be added here */}
          {currentView === 'recording-report' && (
            <div className="coming-soon">
              <h2>Recording Report</h2>
              <p>This feature is coming soon.</p>
            </div>
          )}
          {currentView === 'critical-video' && (
            <div className="coming-soon">
              <h2>Critical Video</h2>
              <p>This feature is coming soon.</p>
            </div>
          )}
          {currentView === 'redundant-playback' && (
            <div className="coming-soon">
              <h2>Redundant Playback</h2>
              <p>This feature is coming soon.</p>
            </div>
          )}
        </div>
      );
    case 'Settings':
      return (
        <div className="settings-content">
          <SettingsContent selectedMenu={settingsMenu} />
        </div>
      );
    default:
      return null;
  }
}

function App() {
  const [activeTab, setActiveTab] = useState('Dashboard');
  const [currentView, setCurrentView] = useState('camera');
  const [showCollectionManager, setShowCollectionManager] = useState(false);
  const [settingsMenu, setSettingsMenu] = useState('software-settings');
  const [selectedRecordingId, setSelectedRecordingId] = useState(null);
  const [eventsMenu, setEventsMenu] = useState('search-events');
  const { loadCameraConfig } = useCameraStore();
  const { isAuthenticated, currentUser, logout } = useUserStore();

  // Log authentication state changes
  useEffect(() => {
    console.log("Authentication state:", { isAuthenticated, currentUser });
  }, [isAuthenticated, currentUser]);

  useEffect(() => {
    // Load camera configuration from backend API when app starts
    console.log('Loading camera configuration from backend API');
    loadCameraConfig();
  }, [loadCameraConfig]);

  const handleViewChange = (newView) => {
    console.log('Changing view to:', newView);
    setCurrentView(newView);
    // If changing to camera view, don't automatically show collection manager
    if (newView !== 'camera') {
      setShowCollectionManager(false);
    }
  };

  // Removed unused handleCreateCollection function

  const handleSettingsMenuSelect = (menuId) => {
    setSettingsMenu(menuId);
  };

  const handleEventsMenuSelect = (menuId) => {
    setEventsMenu(menuId);
  };

  const handleLoginSuccess = () => {
    // Set default tab after login
    setActiveTab('Dashboard');
    setCurrentView('camera');
  };

  // If not authenticated, show login screen
  if (!isAuthenticated) {
    return <IllustratedLogin onLoginSuccess={handleLoginSuccess} />;
  }

  return (
    <Router>
      <Routes>
        <Route path="*" element={
          <DndProvider backend={HTML5Backend}>
            <CameraProvider>
              <div className="App">
                <TabBar
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                  onLogout={logout}
                  currentUser={currentUser}
                />
                <div className="content-wrapper">
                  {/* Single persistent sidebar that changes content based on active tab */}
                  <UniversalSidebar title={
                    activeTab === 'Dashboard' ? 'VMS' :
                    activeTab === 'Events' ? 'Events' :
                    activeTab === 'Archive' ? 'Archive' :
                    activeTab === 'Configuration' ? 'Configuration' :
                    activeTab === 'Settings' ? 'Settings' : 'VMS'
                  }>
                    {activeTab === 'Dashboard' && (
                      <MainSidebar
                        onViewChange={handleViewChange}
                      />
                    )}
                    {activeTab === 'Events' && (
                      <EventsSidebar onMenuSelect={handleEventsMenuSelect} />
                    )}
                    {activeTab === 'Archive' && (
                      <ArchiveSidebar onMenuSelect={handleViewChange} />
                    )}
                    {activeTab === 'Configuration' && (
                      <ConfigurationSidebar
                        onViewChange={handleViewChange}
                        currentUser={currentUser}
                      />
                    )}
                    {activeTab === 'Settings' && (
                      <SettingsSidebar onMenuSelect={handleSettingsMenuSelect} />
                    )}
                  </UniversalSidebar>
                  <main className="App-main">
                    <TabContent
                      activeTab={activeTab}
                      currentView={currentView}
                      onViewChange={handleViewChange}
                      showCollectionManager={showCollectionManager}
                      setShowCollectionManager={setShowCollectionManager}
                      settingsMenu={settingsMenu}
                      selectedRecordingId={selectedRecordingId}
                      eventsMenu={eventsMenu}
                      onSelectRecording={setSelectedRecordingId}
                    />
                  </main>
                </div>
              </div>
            </CameraProvider>
          </DndProvider>
        } />
      </Routes>
    </Router>
  );
}

export default App;