// Enhanced Archive Playback with modern UI
import React, { useState, useEffect, useRef } from 'react';
import {
  Video,
  Calendar,
  Filter,
  Search,
  RefreshCw,
  Grid,
  List,
  Play,
  Pause,
  Download,
  Clock,
  HardDrive,
  ChevronLeft,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useArchiveStore } from '../../store/archiveStore';
import archiveApi from '../../services/archiveApi';
import './ArchivePlaybackContent.css';

const ArchivePlaybackContent = ({ onSelectRecording, selectedRecordingId }) => {
  const {
    recordings,
    availableStreams,
    isLoading,
    error,
    filters,
    selectedStreamId,
    loadRecordings,
    loadAvailableStreams,
    setFilters,
    getFilteredRecordings,
    clearError,
    startStatusPolling,
    stopStatusPolling,
    restartRecordings
  } = useArchiveStore();

  // Enhanced state management
  const [filteredRecordings, setFilteredRecordings] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedRecording, setSelectedRecording] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [filterDate, setFilterDate] = useState('today');
  const [filterCamera, setFilterCamera] = useState('all');
  const [viewMode, setViewMode] = useState('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [selectionStart, setSelectionStart] = useState(null);
  const [selectionEnd, setSelectionEnd] = useState(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [downloadFormat, setDownloadFormat] = useState('mp4');

  // Refs
  const videoRef = useRef(null);
  const timelineRef = useRef(null);

  // Initialize component
  useEffect(() => {
    loadRecordings();
    loadAvailableStreams();
    startStatusPolling();

    return () => {
      stopStatusPolling();
    };
  }, [loadRecordings, loadAvailableStreams, startStatusPolling, stopStatusPolling]);

  // Update filtered recordings when recordings or filters change
  useEffect(() => {
    const filtered = getFilteredRecordings();
    setFilteredRecordings(filtered);
  }, [recordings, filters, getFilteredRecordings]);

  // Handle selected recording from parent
  useEffect(() => {
    if (selectedRecordingId) {
      const recording = recordings.find(r => r.filename === selectedRecordingId);
      if (recording) {
        setSelectedRecording(recording);
      }
    }
  }, [selectedRecordingId, recordings]);

  // Get available streams as array
  const getAvailableStreamsArray = () => {
    if (Array.isArray(availableStreams)) {
      return availableStreams;
    }
    if (typeof availableStreams === 'object' && availableStreams !== null) {
      return Object.keys(availableStreams);
    }
    return [];
  };

  // Enhanced refresh function
  const handleRefresh = async () => {
    setIsRefreshing(true);
    clearError();
    try {
      await Promise.all([
        loadRecordings(),
        loadAvailableStreams()
      ]);
    } catch (err) {
      console.error('Failed to refresh:', err);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Enhanced video player functions
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimelineClick = (e) => {
    if (!timelineRef.current || !videoRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    if (isSelecting) {
      if (selectionStart === null) {
        setSelectionStart(newTime);
      } else if (selectionEnd === null) {
        setSelectionEnd(newTime);
        setIsSelecting(false);
      }
    } else {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const handleVideoTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleVideoLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  // Enhanced utility functions
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Enhanced download functionality
  const downloadRecording = (recording) => {
    if (!recording) return;

    // Create download URL - try multiple possible endpoints
    const downloadUrl = `/recordings/${recording.filename}`;

    // Create temporary download link
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = recording.filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadSegment = () => {
    if (selectionStart !== null && selectionEnd !== null && selectedRecording) {
      const startTime = Math.min(selectionStart, selectionEnd);
      const endTime = Math.max(selectionStart, selectionEnd);

      // Create download URL with time parameters
      const downloadUrl = `/api/recordings/${selectedRecording.filename}/download?start=${startTime}&end=${endTime}&format=${downloadFormat}`;

      // Create temporary download link
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${selectedRecording.stream_id}_${formatTime(startTime)}-${formatTime(endTime)}.${downloadFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Handle recording selection
  const handlePlayRecording = (recording) => {
    setSelectedRecording(recording);
    if (onSelectRecording) {
      onSelectRecording(recording.filename);
    }
  };

  // Enhanced UI Components
  const RecordingStatusIndicator = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-black">Recording Status</h3>
        <div className="flex items-center text-sm text-gray-600">
          <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
          System Active
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {(() => {
          const streams = getAvailableStreamsArray();
          return streams.length > 0 ? (
            streams.map((stream, index) => {
              const streamInfo = archiveApi.parseStreamId ? archiveApi.parseStreamId(stream) : { collectionName: 'Unknown', cameraIp: 'Unknown' };
              const uniqueKey = `${stream || 'unknown'}-${index}`;
              return (
                <div key={uniqueKey} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <Video className="w-4 h-4 mr-2 text-gray-600" />
                    <span className="text-sm font-medium text-black">
                      {streamInfo.collectionName} ({streamInfo.cameraIp})
                    </span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-xs text-gray-600">Active</span>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-3 text-center text-gray-500">
              No active recording streams detected
            </div>
          );
        })()}
      </div>
    </div>
  );

  // Enhanced Filter Bar
  const FilterBar = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Date Range Filter */}
        <div className="filter-card">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="w-4 h-4 inline mr-1" />
            Date Range
          </label>
          <select
            value={filterDate}
            onChange={(e) => setFilterDate(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-lg text-sm"
          >
            <option value="today">Today</option>
            <option value="yesterday">Yesterday</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="all">All Time</option>
          </select>
        </div>

        {/* Camera Filter */}
        <div className="filter-card">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Video className="w-4 h-4 inline mr-1" />
            Camera
          </label>
          <select
            value={filterCamera}
            onChange={(e) => setFilterCamera(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-lg text-sm"
          >
            <option value="all">All Cameras</option>
            {getAvailableStreamsArray().map((stream, index) => {
              const streamInfo = archiveApi.parseStreamId ? archiveApi.parseStreamId(stream) : { collectionName: 'Unknown', cameraIp: 'Unknown' };
              return (
                <option key={`${stream}-${index}`} value={stream}>
                  {streamInfo.collectionName} ({streamInfo.cameraIp})
                </option>
              );
            })}
          </select>
        </div>

        {/* Sort Filter */}
        <div className="filter-card">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Filter className="w-4 h-4 inline mr-1" />
            Sort By
          </label>
          <select className="w-full p-2 border border-gray-300 rounded-lg text-sm">
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="size">File Size</option>
            <option value="duration">Duration</option>
          </select>
        </div>

        {/* Search and Controls */}
        <div className="filter-card">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Search className="w-4 h-4 inline mr-1" />
            Search
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Search recordings..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 p-2 border border-gray-300 rounded-lg text-sm"
            />
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* View Mode Toggle */}
      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
        <div className="text-sm text-gray-600">
          {filteredRecordings.length} recording{filteredRecordings.length !== 1 ? 's' : ''} found
        </div>
      </div>
    </div>
  );

  return (
    <div className="archive-playback-content">
      <div className="recordings-container">
        {/* Enhanced Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-black">Archive Playback</h1>
        </div>

        <RecordingStatusIndicator />
        <FilterBar />

        {/* Enhanced Recording Card Component */}
        {selectedRecording ? (
          <div className="p-4 lg:p-6">
            <div className="flex items-center mb-4">
              <button
                onClick={() => setSelectedRecording(null)}
                className="flex items-center text-gray-600 hover:text-black mr-4"
              >
                <ChevronLeft className="w-5 h-5 mr-1" />
                Back to Recordings
              </button>
            </div>

            {/* Enhanced Video Player with Fixed Source */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-4">
              <div className="bg-gray-900 relative">
                <video
                  ref={videoRef}
                  className="w-full h-64 md:h-96 object-cover"
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onTimeUpdate={handleVideoTimeUpdate}
                  onLoadedMetadata={handleVideoLoadedMetadata}
                  onError={(e) => {
                    console.error('Video loading error:', e);
                    console.log('Recording filename:', selectedRecording.filename);
                  }}
                  aria-label="Archive recording video player"
                  controls
                >
                  {/* Try multiple possible video source paths */}
                  <source src={`/recordings/${selectedRecording.filename}`} type="video/mp4" />
                  <source src={`/api/recordings/${selectedRecording.filename}`} type="video/mp4" />
                  <source src={`./recordings/${selectedRecording.filename}`} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <button
                    onClick={handlePlayPause}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white p-4 rounded-full transition-all pointer-events-auto"
                  >
                    {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                  </button>
                </div>
              </div>

              {/* Enhanced Video Controls */}
              <div className="p-4">
                {/* Timeline */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Timeline</span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setIsSelecting(!isSelecting)}
                        className={`text-xs px-2 py-1 rounded ${isSelecting ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}
                      >
                        {isSelecting ? 'Cancel Selection' : 'Select Range'}
                      </button>
                      {selectionStart !== null && selectionEnd !== null && (
                        <button
                          onClick={downloadSegment}
                          className="text-xs px-2 py-1 bg-green-100 text-green-600 rounded"
                        >
                          <Download className="w-3 h-3 inline mr-1" />
                          Download Segment
                        </button>
                      )}
                      <button
                        onClick={() => downloadRecording(selectedRecording)}
                        className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded"
                      >
                        <Download className="w-3 h-3 inline mr-1" />
                        Download Full
                      </button>
                    </div>
                  </div>

                  <div className="relative">
                    <div
                      ref={timelineRef}
                      className="w-full h-2 bg-gray-300 rounded-full cursor-pointer"
                      onClick={handleTimelineClick}
                    >
                      {/* Progress bar */}
                      <div
                        className="h-full bg-blue-500 rounded-full"
                        style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                      />

                      {/* Selection range */}
                      {selectionStart !== null && selectionEnd !== null && duration > 0 && (
                        <div
                          className="absolute top-0 h-full bg-blue-200 rounded-full"
                          style={{
                            left: `${(Math.min(selectionStart, selectionEnd) / duration) * 100}%`,
                            width: `${(Math.abs(selectionEnd - selectionStart) / duration) * 100}%`
                          }}
                        />
                      )}

                      {/* Selection markers */}
                      {selectionStart !== null && duration > 0 && (
                        <div
                          className="absolute top-0 w-2 h-full bg-blue-500 rounded-full"
                          style={{ left: `${(selectionStart / duration) * 100}%` }}
                        />
                      )}
                      {selectionEnd !== null && duration > 0 && (
                        <div
                          className="absolute top-0 w-2 h-full bg-blue-500 rounded-full"
                          style={{ left: `${(selectionEnd / duration) * 100}%` }}
                        />
                      )}
                    </div>

                    {/* Time markers */}
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>{formatTime(currentTime)}</span>
                      <span>{formatTime(duration)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recording Info */}
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-black">
                    {selectedRecording?.stream_id && archiveApi.parseStreamId ?
                      archiveApi.parseStreamId(selectedRecording.stream_id).collectionName :
                      'Unknown Camera'}
                  </h3>
                  <span className="text-sm text-gray-600">
                    {selectedRecording?.stream_id && archiveApi.parseStreamId ?
                      archiveApi.parseStreamId(selectedRecording.stream_id).cameraIp :
                      'Unknown IP'}
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatDate(selectedRecording.timestamp)}
                  </div>
                  <div className="flex items-center">
                    <Video className="w-4 h-4 mr-1" />
                    {archiveApi.formatRecordingDuration ?
                      archiveApi.formatRecordingDuration(selectedRecording) :
                      'Unknown Duration'}
                  </div>
                  <div className="flex items-center">
                    <HardDrive className="w-4 h-4 mr-1" />
                    Size: {archiveApi.formatFileSize ?
                      archiveApi.formatFileSize(selectedRecording.size_bytes) :
                      'Unknown Size'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="archive-content-area">
            {/* Loading State */}
            {isLoading && (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <h3>Loading Archive</h3>
                <p>Fetching recordings from all cameras...</p>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="error-container">
                <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
                <h3>Unable to Load Archive</h3>
                <p className="error-message">
                  {error}
                </p>
                {error.includes('backend is not available') && (
                  <div className="backend-unavailable-info">
                    <p className="info-text">
                      The archive backend is currently not running. To access recordings:
                    </p>
                    <ul className="info-list">
                      <li>Start the Python backend server</li>
                      <li>Ensure the archive recording service is running</li>
                      <li>Check that recordings exist in the ./recordings/ directory</li>
                    </ul>
                  </div>
                )}
                <button onClick={handleRefresh} className="retry-button">
                  Try Again
                </button>
              </div>
            )}

            {/* Enhanced Recordings Grid */}
            {!isLoading && !error && (
              <div className={`grid gap-4 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {filteredRecordings.length === 0 ? (
                  <div className="col-span-full">
                    <div className="no-recordings">
                      <div className="no-recordings-icon">📁</div>
                      <h3>No archived recordings found</h3>
                      <p>
                        {selectedStreamId
                          ? "No recordings available for the selected camera."
                          : "No recordings are currently available from any camera."
                        }
                      </p>
                      <p className="hint">
                        Recordings will appear here when cameras start recording and files are saved.
                      </p>
                      <button onClick={handleRefresh} className="refresh-button">
                        <RefreshCw className="refresh-icon" />
                        Refresh Archive
                      </button>
                    </div>
                  </div>
                ) : (
                  filteredRecordings.map(recording => (
                    <div
                      key={recording.filename}
                      className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => handlePlayRecording(recording)}
                    >
                      <div className="relative">
                        <div className="w-full h-32 bg-gray-100 flex items-center justify-center">
                          <Video className="w-8 h-8 text-gray-400" />
                        </div>
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          {archiveApi.formatRecordingDuration ?
                            archiveApi.formatRecordingDuration(recording) :
                            'Unknown'}
                        </div>
                      </div>
                      <div className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-black text-sm">
                            {recording?.stream_id && archiveApi.parseStreamId ?
                              archiveApi.parseStreamId(recording.stream_id).collectionName :
                              'Unknown Camera'}
                          </h4>
                          <span className="text-xs text-gray-600">
                            {archiveApi.formatFileSize ?
                              archiveApi.formatFileSize(recording.size_bytes) :
                              'Unknown Size'}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600 mb-1">
                          {formatDate(recording.timestamp)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {recording?.stream_id && archiveApi.parseStreamId ?
                            archiveApi.parseStreamId(recording.stream_id).cameraIp :
                            'Unknown IP'}
                        </div>
                        <div className="flex items-center justify-between mt-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePlayRecording(recording);
                            }}
                            className="text-xs px-3 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                          >
                            <Play className="w-3 h-3 inline mr-1" />
                            Play
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              downloadRecording(recording);
                            }}
                            className="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded hover:bg-gray-200"
                          >
                            <Download className="w-3 h-3 inline mr-1" />
                            Download
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(ArchivePlaybackContent);
