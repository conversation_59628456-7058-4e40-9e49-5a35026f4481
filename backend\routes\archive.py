from fastapi import APIRouter, HTTPException, status, Request
from fastapi.responses import StreamingResponse, JSONResponse
import os
import logging
import re
from typing import List, Dict, Optional
from pathlib import Path
import mimetypes
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/archive", tags=["archive"])

# Global reference to archive manager (will be set by main.py)
archive_manager = None

def set_archive_manager(manager):
    """Set the global archive manager instance"""
    global archive_manager
    archive_manager = manager

@router.get("/list/{stream_id}")
async def list_recordings(stream_id: str):
    """Get list of available recording files for a specific stream"""
    try:
        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )
        
        # Validate stream_id format (should be collection_ip)
        if not stream_id or '_' not in stream_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid stream ID format. Expected: collection_ip"
            )
        
        recordings = archive_manager.get_available_recordings(stream_id)
        
        # Get detailed info for each recording
        recording_details = []
        for filename in recordings:
            info = archive_manager.get_recording_info(stream_id, filename)
            if info:
                recording_details.append(info)
        
        return {
            "stream_id": stream_id,
            "recordings": recording_details,
            "count": len(recording_details)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing recordings for {stream_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving recordings: {str(e)}"
        )

@router.options("/stream/{stream_id}/{filename}")
async def stream_recording_options(stream_id: str, filename: str):
    """Handle CORS preflight requests for video streaming"""
    return JSONResponse(
        content={},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
            "Access-Control-Allow-Headers": "Range, Content-Type, Accept, Authorization",
            "Access-Control-Max-Age": "86400"  # Cache preflight for 24 hours
        }
    )

@router.get("/validate/{stream_id}/{filename}")
async def validate_recording(stream_id: str, filename: str):
    """Validate a recording file and return detailed information"""
    try:
        logger.info(f"Validating recording: {stream_id}/{filename}")

        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        # Validate stream_id format
        if not stream_id or '_' not in stream_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid stream ID format. Expected: collection_ip"
            )

        # Validate filename
        if not filename.endswith('.mp4'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format. Only .mp4 files are supported"
            )

        # Get the recording file path
        recording_path = archive_manager.get_recording_path(stream_id, filename)

        if not recording_path or not recording_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Recording file not found: {filename}"
            )

        # Get basic file info
        file_stat = recording_path.stat()
        file_size = file_stat.st_size

        # Try to get video information using ffprobe if available
        video_info = {
            "filename": filename,
            "file_size": file_size,
            "file_size_mb": round(file_size / (1024 * 1024), 2),
            "exists": True,
            "is_readable": True,
            "path": str(recording_path)
        }

        # Basic validation
        if file_size == 0:
            video_info["is_valid"] = False
            video_info["issues"] = ["File is empty"]
        elif file_size < 1024:
            video_info["is_valid"] = False
            video_info["issues"] = ["File is too small (less than 1KB)"]
        else:
            video_info["is_valid"] = True
            video_info["issues"] = []

        # Try to read first few bytes to check for MP4 signature
        try:
            with open(recording_path, 'rb') as f:
                first_bytes = f.read(32)
                # Check for MP4 file signatures
                if b'ftyp' in first_bytes[:20] or first_bytes[4:8] == b'ftyp':
                    video_info["has_mp4_signature"] = True
                else:
                    video_info["has_mp4_signature"] = False
                    video_info["issues"].append("File does not have valid MP4 signature")
                    video_info["is_valid"] = False
        except Exception as e:
            video_info["has_mp4_signature"] = False
            video_info["issues"].append(f"Cannot read file: {str(e)}")
            video_info["is_valid"] = False

        return JSONResponse(content=video_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating recording {stream_id}/{filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating recording: {str(e)}"
        )

@router.head("/stream/{stream_id}/{filename}")
async def stream_recording_head(stream_id: str, filename: str):
    """Handle HEAD requests for video streaming (used for URL testing)"""
    try:
        logger.info(f"Archive stream HEAD request: {stream_id}/{filename}")

        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        # Validate stream_id format
        if not stream_id or '_' not in stream_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid stream ID format. Expected: collection_ip"
            )

        # Validate filename
        if not filename.endswith('.mp4'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format. Only .mp4 files are supported"
            )

        # Get the recording file path
        recording_path = archive_manager.get_recording_path(stream_id, filename)

        if not recording_path or not recording_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Recording file not found: {filename}"
            )

        # Get file size
        file_size = recording_path.stat().st_size

        if file_size == 0:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Recording file is empty or corrupted: {filename}"
            )

        # Return headers without body using Response instead of JSONResponse for HEAD
        from fastapi import Response
        return Response(
            content="",
            media_type="video/mp4",
            headers={
                "Content-Length": str(file_size),
                "Accept-Ranges": "bytes",
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
                "Access-Control-Allow-Headers": "Range, Content-Type, Accept",
                "Access-Control-Expose-Headers": "Content-Length, Content-Range, Accept-Ranges"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in HEAD request for {stream_id}/{filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error accessing recording: {str(e)}"
        )

@router.get("/stream/{stream_id}/{filename}")
async def stream_recording(stream_id: str, filename: str, request: Request):
    """Stream a specific recording file for browser playback with range support"""
    try:
        logger.info(f"Archive stream request: {stream_id}/{filename}")

        if not archive_manager:
            logger.error("Archive manager not available")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        # Validate stream_id format
        if not stream_id or '_' not in stream_id:
            logger.error(f"Invalid stream ID format: {stream_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid stream ID format. Expected: collection_ip"
            )

        # Validate filename (should be .mp4 and match expected format)
        if not filename.endswith('.mp4'):
            logger.error(f"Invalid file format: {filename}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format. Only .mp4 files are supported"
            )

        # Get the recording file path
        recording_path = archive_manager.get_recording_path(stream_id, filename)
        logger.info(f"Recording path resolved: {recording_path}")

        if not recording_path:
            logger.error(f"Recording path not found for {stream_id}/{filename}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Recording file not found: {filename}"
            )

        # Check if file exists and is readable
        if not recording_path.exists():
            logger.error(f"Recording file does not exist: {recording_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Recording file not found: {filename}"
            )

        # Get file size for Content-Length header
        file_size = recording_path.stat().st_size
        logger.info(f"File size: {file_size} bytes")

        # Validate file size (check if file is not empty or corrupted)
        if file_size == 0:
            logger.error(f"Recording file is empty: {recording_path}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Recording file is empty or corrupted: {filename}"
            )

        # Check if file is currently being written to (very recent modification)
        import time
        file_mtime = recording_path.stat().st_mtime
        current_time = time.time()
        if current_time - file_mtime < 10:  # File modified within last 10 seconds
            logger.warning(f"File {recording_path} was recently modified, may still be recording")
            # Still allow streaming, but log the warning

        # Test if file can be opened for reading
        try:
            with open(recording_path, 'rb') as test_file:
                # Try to read first few bytes to ensure file is accessible
                test_bytes = test_file.read(32)
                if len(test_bytes) < 32 and file_size > 32:
                    logger.error(f"Cannot read expected bytes from {recording_path}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="File appears to be locked or corrupted"
                    )
        except PermissionError:
            logger.error(f"Permission denied reading {recording_path}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="File is currently locked or permission denied"
            )
        except Exception as e:
            logger.error(f"Error testing file access for {recording_path}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Cannot access file: {str(e)}"
            )

        # Determine MIME type - be explicit about video/mp4
        mime_type = "video/mp4"
        logger.info(f"Using MIME type: {mime_type}")

        # Handle HTTP Range requests for video seeking
        range_header = request.headers.get('range')
        logger.info(f"Range header: {range_header}")

        if range_header:
            # Parse range header (e.g., "bytes=0-1023")
            try:
                range_match = re.match(r'bytes=(\d+)-(\d*)', range_header)
                if range_match:
                    start = int(range_match.group(1))
                    end = int(range_match.group(2)) if range_match.group(2) else file_size - 1

                    # Validate range
                    if start >= file_size or end >= file_size or start > end:
                        raise HTTPException(
                            status_code=status.HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE,
                            detail="Invalid range request"
                        )

                    content_length = end - start + 1

                    def range_file_generator():
                        """Generator to stream file range in chunks"""
                        try:
                            with open(recording_path, "rb") as file:
                                file.seek(start)
                                remaining = content_length
                                chunk_size = min(8192, remaining)  # 8KB chunks or remaining bytes

                                while remaining > 0:
                                    chunk = file.read(min(chunk_size, remaining))
                                    if not chunk:
                                        break
                                    remaining -= len(chunk)
                                    yield chunk
                        except Exception as e:
                            logger.error(f"Error streaming file range {recording_path}: {e}")
                            raise

                    # Return partial content response with enhanced headers
                    range_headers = {
                        "Content-Length": str(content_length),
                        "Content-Range": f"bytes {start}-{end}/{file_size}",
                        "Accept-Ranges": "bytes",
                        "Cache-Control": "public, max-age=3600",  # Cache for 1 hour
                        "Content-Disposition": f"inline; filename={filename}",
                        # Additional headers for better video streaming support
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
                        "Access-Control-Allow-Headers": "Range, Content-Type, Accept",
                        "Access-Control-Expose-Headers": "Content-Length, Content-Range, Accept-Ranges",
                        # Video-specific headers
                        "Content-Transfer-Encoding": "binary",
                        "Connection": "keep-alive"
                    }

                    logger.info(f"Streaming range {start}-{end}/{file_size} with headers: {range_headers}")

                    return StreamingResponse(
                        range_file_generator(),
                        status_code=206,  # Partial Content
                        media_type=mime_type,
                        headers=range_headers
                    )
            except Exception as e:
                logger.error(f"Error parsing range header: {e}")
                # Fall through to full file streaming

        # Use FileResponse for simpler, more reliable file serving
        from fastapi.responses import FileResponse

        logger.info(f"Serving file directly: {recording_path}")

        return FileResponse(
            path=str(recording_path),
            media_type="video/mp4",
            filename=filename,
            headers={
                "Accept-Ranges": "bytes",
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
                "Access-Control-Allow-Headers": "Range, Content-Type, Accept",
                "Access-Control-Expose-Headers": "Content-Length, Content-Range, Accept-Ranges",
                "Content-Transfer-Encoding": "binary",
                "Connection": "keep-alive"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error streaming recording {stream_id}/{filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error streaming recording: {str(e)}"
        )

@router.get("/status")
async def get_archive_status():
    """Get status of the archive recording system"""
    try:
        if not archive_manager:
            return {
                "status": "unavailable",
                "message": "Archive recording service not initialized"
            }

        status_info = archive_manager.get_recording_status()

        result = {
            "status": "active",
            "timestamp": datetime.now().isoformat(),
            **status_info
        }

        return result

    except Exception as e:
        logger.error(f"Error getting archive status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting archive status: {str(e)}"
        )

@router.get("/streams")
async def list_available_streams(force_refresh: bool = False):
    """Get list of all streams that have recordings available"""
    try:
        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        logger.info(f"Getting available streams (force_refresh={force_refresh})")

        # Get all recordings with optional force refresh
        all_recordings = archive_manager.get_all_recordings(force_refresh=force_refresh)

        # Group recordings by stream_id
        streams_dict = {}
        for recording in all_recordings:
            stream_id = recording['stream_id']
            if stream_id not in streams_dict:
                # Parse stream_id to get collection and IP
                parts = stream_id.split('_', 1)
                collection_name = parts[0] if len(parts) > 0 else "unknown"
                camera_ip = parts[1] if len(parts) > 1 else "unknown"

                streams_dict[stream_id] = {
                    "stream_id": stream_id,
                    "collection_name": collection_name,
                    "camera_ip": camera_ip,
                    "recording_count": 0,
                    "latest_recording": None
                }

            streams_dict[stream_id]["recording_count"] += 1

            # Update latest recording (recordings are already sorted newest first)
            if not streams_dict[stream_id]["latest_recording"]:
                streams_dict[stream_id]["latest_recording"] = recording['filename']

        # Convert to list
        available_streams = list(streams_dict.values())

        # Sort by stream_id for consistent ordering
        available_streams.sort(key=lambda x: x['stream_id'])

        logger.info(f"Found {len(available_streams)} streams with recordings")

        return {
            "streams": available_streams,
            "count": len(available_streams)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing available streams: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing streams: {str(e)}"
        )

@router.get("/recordings")
async def get_all_recordings(force_refresh: bool = False):
    """Get all recordings across all streams"""
    try:
        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        logger.info(f"Getting all recordings (force_refresh={force_refresh})")
        recordings = archive_manager.get_all_recordings(force_refresh=force_refresh)

        return {
            "recordings": recordings,
            "count": len(recordings)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting all recordings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get all recordings: {str(e)}"
        )

@router.delete("/recordings/{stream_id}/{filename}")
async def delete_recording(stream_id: str, filename: str):
    """Delete a specific recording file (admin function)"""
    try:
        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )
        
        # Validate inputs
        if not stream_id or '_' not in stream_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid stream ID format"
            )
        
        if not filename.endswith('.mp4'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format"
            )
        
        # Get the recording file path
        recording_path = archive_manager.get_recording_path(stream_id, filename)
        
        if not recording_path or not recording_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Recording file not found: {filename}"
            )
        
        # Delete the file
        recording_path.unlink()
        logger.info(f"Deleted recording: {recording_path}")
        
        return {
            "status": "success",
            "message": f"Recording {filename} deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting recording {stream_id}/{filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting recording: {str(e)}"
        )

@router.post("/restart")
async def restart_recordings():
    """Restart failed recording processes"""
    try:
        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        # Restart failed recordings
        archive_manager.restart_failed_recordings()

        # Get updated status
        status_info = archive_manager.get_recording_status()

        return {
            "status": "success",
            "message": "Recording restart completed",
            "timestamp": datetime.now().isoformat(),
            **status_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error restarting recordings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error restarting recordings: {str(e)}"
        )

@router.get("/current")
async def get_current_recordings():
    """Get list of current/active recordings with real-time status"""
    try:
        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        logger.info("Getting current recordings")

        # Get recordings directory
        recordings_dir = Path("recordings")
        if not recordings_dir.exists():
            return JSONResponse({
                "current_recordings": [],
                "total_active": 0
            })

        current_recordings = []

        # Iterate through stream directories
        for stream_dir in recordings_dir.iterdir():
            if not stream_dir.is_dir():
                continue

            stream_id = stream_dir.name

            # Skip quarantine directory - these are corrupted files
            if stream_id == "quarantine":
                continue

            # Validate stream_id format (should be collection_ip)
            if not stream_id or '_' not in stream_id:
                continue

            # Get all MP4 files in this stream directory
            mp4_files = list(stream_dir.glob("*.mp4"))

            for mp4_file in mp4_files:
                try:
                    file_stat = mp4_file.stat()
                    file_size = file_stat.st_size

                    # Skip empty or very small files (likely corrupted)
                    if file_size < 1024:  # Less than 1KB
                        continue

                    # Check if file is currently being written to
                    import time
                    current_time = time.time()
                    last_modified = file_stat.st_mtime
                    time_since_modified = current_time - last_modified

                    # Consider a file "recording" if it was modified within the last 60 seconds
                    is_recording = time_since_modified < 60

                    # Calculate approximate duration (rough estimate based on file size)
                    # Assuming ~1MB per minute for typical video compression
                    duration_seconds = max(1, int(file_size / (1024 * 1024 / 60)))

                    recording_info = {
                        "stream_id": stream_id,
                        "filename": mp4_file.name,
                        "size": file_size,
                        "start_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                        "last_modified": datetime.fromtimestamp(last_modified).isoformat(),
                        "is_recording": is_recording,
                        "duration_seconds": duration_seconds
                    }

                    current_recordings.append(recording_info)

                except Exception as e:
                    logger.warning(f"Error processing file {mp4_file}: {e}")
                    continue

        # Sort by last modified time (newest first)
        current_recordings.sort(key=lambda x: x["last_modified"], reverse=True)

        logger.info(f"Found {len(current_recordings)} current recordings")

        return JSONResponse({
            "current_recordings": current_recordings,
            "total_active": len(current_recordings)
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current recordings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting current recordings: {str(e)}"
        )

@router.get("/convert/{stream_id}/{filename}")
async def convert_recording(stream_id: str, filename: str, format: str = "mp4"):
    """Convert a recording to browser-compatible format using FFmpeg"""
    import subprocess
    import tempfile
    import shutil

    try:
        logger.info(f"Converting recording: {stream_id}/{filename}")

        if not archive_manager:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Archive recording service not available"
            )

        # Validate stream_id format
        if not stream_id or '_' not in stream_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid stream ID format. Expected: collection_ip"
            )

        # Validate filename
        if not filename.endswith('.mp4'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format. Only .mp4 files are supported"
            )

        # Get the original recording file path
        recording_path = archive_manager.get_recording_path(stream_id, filename)

        if not recording_path or not recording_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Recording file not found: {filename}"
            )

        # Create converted filename
        base_name = filename.rsplit('.', 1)[0]
        converted_filename = f"{base_name}_converted.mp4"
        converted_path = recording_path.parent / converted_filename

        # Check if converted file already exists
        if converted_path.exists():
            logger.info(f"Converted file already exists: {converted_path}")
            return JSONResponse({
                "status": "success",
                "message": "File already converted",
                "converted_filename": converted_filename,
                "original_filename": filename
            })

        # Find FFmpeg executable - check local installation first
        backend_dir = Path(__file__).parent.parent
        local_ffmpeg = backend_dir / "ffmpeg-master-latest-win64-gpl-shared" / "bin" / "ffmpeg.exe"

        if local_ffmpeg.exists():
            ffmpeg_path = str(local_ffmpeg)
        else:
            # Fall back to system PATH
            ffmpeg_path = shutil.which("ffmpeg")
            if not ffmpeg_path:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="FFmpeg not found. Please install FFmpeg to use video conversion."
                )

        # Create temporary output file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_output_path = temp_file.name

        try:
            # FFmpeg command for browser-compatible conversion
            cmd = [
                ffmpeg_path,
                "-i", str(recording_path),
                "-c:v", "libx264",  # H.264 codec (widely supported)
                "-c:a", "aac",      # AAC audio codec
                "-preset", "fast",   # Fast encoding
                "-crf", "23",       # Good quality
                "-movflags", "+faststart",  # Optimize for web streaming
                "-y",               # Overwrite output file
                temp_output_path
            ]

            logger.info(f"Running FFmpeg command: {' '.join(cmd)}")

            # Run FFmpeg conversion
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )

            if result.returncode != 0:
                logger.error(f"FFmpeg conversion failed: {result.stderr}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Video conversion failed: {result.stderr}"
                )

            # Move temporary file to final location
            shutil.move(temp_output_path, converted_path)
            logger.info(f"Conversion successful: {converted_path}")

            return JSONResponse({
                "status": "success",
                "message": "Video converted successfully",
                "converted_filename": converted_filename,
                "original_filename": filename,
                "conversion_log": result.stdout
            })

        finally:
            # Clean up temporary file if it still exists
            if os.path.exists(temp_output_path):
                try:
                    os.unlink(temp_output_path)
                except:
                    pass

    except subprocess.TimeoutExpired:
        logger.error(f"FFmpeg conversion timeout for {stream_id}/{filename}")
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Video conversion timed out"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error converting recording {stream_id}/{filename}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error converting recording: {str(e)}"
        )
